# Optimisation des Performances d'Entraînement

Ce guide explique comment utiliser les nouvelles optimisations de performance pour accélérer vos entraînements de modèles CNN, LSTM et attention.

## 🚀 Améliorations Apportées

### 1. Configuration TensorFlow Optimisée (`common/tensorflow_config.py`)

- **Détection automatique des ressources système** (CPU, mémoire)
- **Configuration adaptative des threads** selon votre matériel
- **Optimisations XLA et OneDNN** activées
- **Gestion mémoire optimisée** pour CPU uniquement

### 2. Optimiseur de Performances (`common/performance_optimizer.py`)

- **Calcul automatique des tailles de batch optimales**
- **Configuration Optuna adaptative** (parallélisation intelligente)
- **Recommandations personnalisées** selon votre système
- **Surveillance des ressources** en temps réel

### 3. Parallélisation Améliorée

- **Optuna multi-jobs** avec configuration adaptative
- **Pruning agressif** pour éliminer les trials non prometteurs
- **Validation croisée temporelle** optimisée

## 📊 Gains de Performance Attendus

| Composant | Amélioration Typique | Conditions |
|-----------|---------------------|------------|
| **TensorFlow** | 2-4x plus rapide | CPU 4+ cœurs |
| **Optuna** | 1.5-3x plus rapide | Parallélisation activée |
| **Utilisation CPU** | 60-90% vs 20-40% | Configuration optimisée |
| **Mémoire** | Réduction 20-30% | Gestion adaptative |

## 🛠️ Installation et Configuration

### 1. Vérifier les Dépendances

```bash
# Vérifier que les packages nécessaires sont installés
pip install psutil tensorflow optuna scikit-learn numpy pandas
```

### 2. Tester la Configuration

```bash
# Tester la configuration TensorFlow optimisée
python test_tensorflow_config.py

# Benchmark complet des performances
python benchmark_performance.py
```

### 3. Vérifier les Optimisations

La configuration TensorFlow optimisée est automatiquement appliquée lors de l'import de `models_utils.py`. Vous devriez voir des messages comme :

```
🔧 Configuration TensorFlow pour CPU
💻 Système: Darwin arm64
🧠 CPU: 8 cœurs logiques, 8 cœurs physiques
💾 Mémoire: 14.2GB disponible / 16.0GB total
🔄 Threads intra-op: 8
🔄 Threads inter-op: 2
⚡ Optimisations avancées: activées
✅ Configuration TensorFlow terminée
```

## 📈 Utilisation

### 1. Entraînement Optimisé Automatique

Les optimisations sont appliquées automatiquement dans :

- `optimize_indicators.py` - Utilise maintenant la parallélisation adaptative
- `common/models_utils.py` - Configuration TensorFlow optimisée au démarrage
- Tous les scripts utilisant TensorFlow

### 2. Configuration Manuelle (Optionnel)

```python
from common.performance_optimizer import PerformanceOptimizer

# Créer l'optimiseur
optimizer = PerformanceOptimizer(verbose=True)

# Obtenir la configuration optimale
config = optimizer.get_optimal_config(
    model_complexity="medium",  # "low", "medium", "high"
    dataset_size=5000,
    sequence_length=50,
    n_features=20,
    n_trials=100
)

# Utiliser la configuration
batch_size = config['training']['batch_size']
n_jobs = config['optuna']['n_jobs']
epochs = config['training']['epochs']
```

### 3. Configuration TensorFlow Personnalisée

```python
from common.tensorflow_config import configure_tensorflow_for_cpu

# Configuration avec paramètres personnalisés
config = configure_tensorflow_for_cpu(
    enable_optimizations=True,
    enable_mixed_precision=False,  # Expérimental sur CPU
    custom_thread_config={
        'intra_op': 8,
        'inter_op': 2,
        'omp_threads': 6,
        'mkl_threads': 6
    },
    verbose=True
)
```

## 🔍 Surveillance et Debugging

### 1. Vérifier l'Utilisation CPU

```bash
# Pendant l'entraînement, vérifiez l'utilisation CPU
htop  # ou top sur Linux/macOS
```

Vous devriez voir une utilisation CPU de 60-90% au lieu de 20-40%.

### 2. Logs de Performance

Les logs incluent maintenant des informations de performance :

```
🎯 - BTC_3h - Trials adaptatifs: 180 (demandé: 200, utilisé: 180)
⚡ - BTC_3h - Jobs parallèles optimaux: 4
🚀 - BTC_3h - Début d'optimisation ENHANCED avec 180 trials
```

### 3. Benchmark Personnalisé

```python
from benchmark_performance import PerformanceBenchmark

benchmark = PerformanceBenchmark(verbose=True)

# Tester TensorFlow
tf_results = benchmark.benchmark_tensorflow_operations(iterations=100)

# Tester Optuna
optuna_results = benchmark.benchmark_optuna_performance(n_trials=50)

# Sauvegarder les résultats
benchmark.save_results({'tensorflow': tf_results, 'optuna': optuna_results})
```

## ⚙️ Paramètres Avancés

### 1. Variables d'Environnement

Les variables suivantes sont automatiquement configurées :

```bash
TF_INTRA_OP_PARALLELISM_THREADS=8    # Threads pour opérations TensorFlow
TF_INTER_OP_PARALLELISM_THREADS=2    # Threads entre opérations
OMP_NUM_THREADS=6                    # Threads OpenMP
MKL_NUM_THREADS=6                    # Threads Intel MKL
TF_XLA_FLAGS="--tf_xla_auto_jit=2"   # Optimisations XLA
TF_ENABLE_ONEDNN_OPTS=1              # Optimisations OneDNN
```

### 2. Ajustements Manuels

Pour des cas spécifiques, vous pouvez ajuster :

```python
# Dans optimize_indicators.py, modifier :
perf_config = perf_optimizer.get_optimal_optuna_config(n_trials, len(df))

# Forcer un nombre de jobs spécifique
perf_config['n_jobs'] = 2  # Par exemple, pour limiter l'utilisation mémoire
```

## 🐛 Résolution de Problèmes

### 1. Utilisation CPU Toujours Faible

```bash
# Vérifier la configuration TensorFlow
python -c "
import tensorflow as tf
print('Threads intra-op:', tf.config.threading.get_intra_op_parallelism_threads())
print('Threads inter-op:', tf.config.threading.get_inter_op_parallelism_threads())
print('JIT activé:', tf.config.optimizer.get_jit())
"
```

### 2. Erreurs de Mémoire

Si vous rencontrez des erreurs de mémoire :

```python
# Réduire la parallélisation
os.environ['TF_INTRA_OP_PARALLELISM_THREADS'] = '4'  # Au lieu de 8
os.environ['OMP_NUM_THREADS'] = '4'  # Au lieu de 6

# Ou utiliser la configuration conservatrice
from common.tensorflow_config import configure_tensorflow_for_cpu
configure_tensorflow_for_cpu(
    custom_thread_config={
        'intra_op': 4,
        'inter_op': 1,
        'omp_threads': 2,
        'mkl_threads': 2
    }
)
```

### 3. Optuna Ne Se Parallélise Pas

Vérifiez que vous n'utilisez pas de ressources partagées dans l'objective function :

```python
# ❌ Problématique - ressource partagée
global_model = create_model()

def objective(trial):
    return train_model(global_model, trial.suggest_float('lr', 1e-4, 1e-2))

# ✅ Correct - ressources isolées
def objective(trial):
    model = create_model()  # Créer dans chaque trial
    return train_model(model, trial.suggest_float('lr', 1e-4, 1e-2))
```

## 📊 Métriques de Performance

### Avant Optimisation (Typique)
- **Utilisation CPU** : 20-40%
- **Temps par trial** : 45-60 secondes
- **Parallélisation Optuna** : Désactivée (n_jobs=1)
- **Optimisations TensorFlow** : Basiques

### Après Optimisation (Attendu)
- **Utilisation CPU** : 60-90%
- **Temps par trial** : 15-25 secondes
- **Parallélisation Optuna** : 2-4 jobs selon le système
- **Optimisations TensorFlow** : XLA, OneDNN, threads optimisés

## 🎯 Prochaines Étapes

1. **Testez la configuration** avec `test_tensorflow_config.py`
2. **Lancez un benchmark** avec `benchmark_performance.py`
3. **Surveillez les performances** pendant vos entraînements habituels
4. **Ajustez si nécessaire** selon vos résultats

## 📞 Support

Si vous rencontrez des problèmes :

1. Vérifiez les logs pour les messages d'erreur
2. Lancez le script de test pour diagnostiquer
3. Consultez la section résolution de problèmes
4. Ajustez la configuration selon votre matériel

Les optimisations sont conçues pour être **sûres et adaptatives** - elles ne devraient pas casser votre code existant tout en améliorant significativement les performances.
