#!/usr/bin/env python3
"""
Benchmark des performances d'entraînement
=========================================

Ce script compare les performances d'entraînement avant et après optimisation.
Il mesure l'utilisation CPU, la vitesse d'entraînement et l'efficacité globale.

Usage:
    python benchmark_performance.py
"""

import time
import psutil
import threading
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import json
import os


class PerformanceBenchmark:
    """
    Classe pour benchmarker les performances d'entraînement.
    """
    
    def __init__(self, verbose: bool = True):
        """
        Initialise le benchmark.
        
        Args:
            verbose: Afficher les informations détaillées
        """
        self.verbose = verbose
        self.monitoring = False
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'timestamps': []
        }
        
    def start_monitoring(self):
        """
        Démarre la surveillance des ressources système.
        """
        self.monitoring = True
        self.metrics = {'cpu_usage': [], 'memory_usage': [], 'timestamps': []}
        
        def monitor():
            while self.monitoring:
                try:
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    memory_percent = psutil.virtual_memory().percent
                    timestamp = time.time()
                    
                    self.metrics['cpu_usage'].append(cpu_percent)
                    self.metrics['memory_usage'].append(memory_percent)
                    self.metrics['timestamps'].append(timestamp)
                    
                    time.sleep(0.5)  # Échantillonnage toutes les 0.5 secondes
                except Exception as e:
                    if self.verbose:
                        print(f"⚠️ Erreur monitoring: {e}")
                    break
        
        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()
        
        if self.verbose:
            print("📊 Surveillance des performances démarrée")
    
    def stop_monitoring(self):
        """
        Arrête la surveillance des ressources système.
        """
        self.monitoring = False
        time.sleep(1)  # Laisser le temps au thread de se terminer
        
        if self.verbose:
            print("📊 Surveillance des performances arrêtée")
    
    def get_metrics_summary(self) -> dict:
        """
        Calcule un résumé des métriques collectées.
        
        Returns:
            Dict avec les métriques résumées
        """
        if not self.metrics['cpu_usage']:
            return {}
        
        cpu_usage = self.metrics['cpu_usage']
        memory_usage = self.metrics['memory_usage']
        
        duration = self.metrics['timestamps'][-1] - self.metrics['timestamps'][0]
        
        summary = {
            'duration_seconds': round(duration, 2),
            'cpu_mean': round(np.mean(cpu_usage), 2),
            'cpu_max': round(np.max(cpu_usage), 2),
            'cpu_min': round(np.min(cpu_usage), 2),
            'cpu_std': round(np.std(cpu_usage), 2),
            'memory_mean': round(np.mean(memory_usage), 2),
            'memory_max': round(np.max(memory_usage), 2),
            'samples_count': len(cpu_usage)
        }
        
        return summary
    
    def benchmark_tensorflow_operations(self, iterations: int = 100) -> dict:
        """
        Benchmark des opérations TensorFlow de base.
        
        Args:
            iterations: Nombre d'itérations
            
        Returns:
            Dict avec les résultats du benchmark
        """
        if self.verbose:
            print(f"🧪 Benchmark TensorFlow ({iterations} itérations)")
        
        try:
            import tensorflow as tf
            
            # Test 1: Multiplication de matrices
            self.start_monitoring()
            
            size = 500
            a = tf.random.normal((size, size), dtype=tf.float32)
            b = tf.random.normal((size, size), dtype=tf.float32)
            
            start_time = time.time()
            
            for i in range(iterations):
                result = tf.matmul(a, b)
                if i % 20 == 0 and self.verbose:
                    print(f"   Itération {i+1}/{iterations}")
            
            matmul_time = time.time() - start_time
            
            self.stop_monitoring()
            matmul_metrics = self.get_metrics_summary()
            
            # Test 2: Opérations LSTM
            if self.verbose:
                print("🧠 Test LSTM...")
            
            self.start_monitoring()
            
            # Créer un modèle LSTM simple
            model = tf.keras.Sequential([
                tf.keras.layers.LSTM(64, return_sequences=True, input_shape=(10, 5)),
                tf.keras.layers.LSTM(32),
                tf.keras.layers.Dense(1)
            ])
            
            model.compile(optimizer='adam', loss='mse')
            
            # Données de test
            X_test = np.random.random((200, 10, 5)).astype(np.float32)
            y_test = np.random.random((200, 1)).astype(np.float32)
            
            start_time = time.time()
            
            # Entraînement rapide
            model.fit(X_test, y_test, epochs=5, batch_size=32, verbose=0)
            
            lstm_time = time.time() - start_time
            
            self.stop_monitoring()
            lstm_metrics = self.get_metrics_summary()
            
            results = {
                'matmul': {
                    'time_total': round(matmul_time, 4),
                    'time_per_iteration': round(matmul_time / iterations, 6),
                    'gflops': round((size*size*size*iterations)/(matmul_time*1e9), 2),
                    'metrics': matmul_metrics
                },
                'lstm': {
                    'time_total': round(lstm_time, 4),
                    'samples_per_second': round(len(X_test) * 5 / lstm_time, 2),
                    'metrics': lstm_metrics
                }
            }
            
            if self.verbose:
                print(f"   ✅ Multiplication matrices: {results['matmul']['gflops']} GFLOPS")
                print(f"   ✅ LSTM: {results['lstm']['samples_per_second']} échantillons/sec")
            
            return results
            
        except ImportError:
            if self.verbose:
                print("❌ TensorFlow non disponible")
            return {}
        except Exception as e:
            if self.verbose:
                print(f"❌ Erreur benchmark: {e}")
            return {}
    
    def benchmark_optuna_performance(self, n_trials: int = 50) -> dict:
        """
        Benchmark des performances d'optimisation Optuna.
        
        Args:
            n_trials: Nombre de trials
            
        Returns:
            Dict avec les résultats du benchmark
        """
        if self.verbose:
            print(f"🎯 Benchmark Optuna ({n_trials} trials)")
        
        try:
            import optuna
            
            # Fonction objective simple mais représentative
            def objective(trial):
                # Simuler une optimisation de modèle
                x = trial.suggest_float('x', -10, 10)
                y = trial.suggest_float('y', -10, 10)
                z = trial.suggest_int('z', 1, 100)
                
                # Simulation d'un calcul coûteux
                time.sleep(0.01)  # 10ms par trial
                
                return -(x**2 + y**2 + z/100)
            
            self.start_monitoring()
            
            # Test avec 1 job (séquentiel)
            study_seq = optuna.create_study(direction='maximize')
            start_time = time.time()
            study_seq.optimize(objective, n_trials=n_trials, n_jobs=1)
            sequential_time = time.time() - start_time
            
            self.stop_monitoring()
            sequential_metrics = self.get_metrics_summary()
            
            # Test avec jobs parallèles (si possible)
            try:
                from common.performance_optimizer import PerformanceOptimizer
                optimizer = PerformanceOptimizer()
                optimal_jobs = optimizer.get_optimal_workers("optimization")
                
                if optimal_jobs > 1:
                    self.start_monitoring()
                    
                    study_par = optuna.create_study(direction='maximize')
                    start_time = time.time()
                    study_par.optimize(objective, n_trials=n_trials, n_jobs=optimal_jobs)
                    parallel_time = time.time() - start_time
                    
                    self.stop_monitoring()
                    parallel_metrics = self.get_metrics_summary()
                    
                    speedup = sequential_time / parallel_time if parallel_time > 0 else 1
                else:
                    parallel_time = sequential_time
                    parallel_metrics = sequential_metrics
                    speedup = 1
                    optimal_jobs = 1
                    
            except ImportError:
                parallel_time = sequential_time
                parallel_metrics = sequential_metrics
                speedup = 1
                optimal_jobs = 1
            
            results = {
                'sequential': {
                    'time': round(sequential_time, 4),
                    'trials_per_second': round(n_trials / sequential_time, 2),
                    'metrics': sequential_metrics
                },
                'parallel': {
                    'time': round(parallel_time, 4),
                    'trials_per_second': round(n_trials / parallel_time, 2),
                    'jobs': optimal_jobs,
                    'speedup': round(speedup, 2),
                    'metrics': parallel_metrics
                }
            }
            
            if self.verbose:
                print(f"   ✅ Séquentiel: {results['sequential']['trials_per_second']} trials/sec")
                print(f"   ✅ Parallèle ({optimal_jobs} jobs): {results['parallel']['trials_per_second']} trials/sec")
                print(f"   🚀 Accélération: {speedup:.2f}x")
            
            return results
            
        except ImportError:
            if self.verbose:
                print("❌ Optuna non disponible")
            return {}
        except Exception as e:
            if self.verbose:
                print(f"❌ Erreur benchmark Optuna: {e}")
            return {}
    
    def save_results(self, results: dict, filename: str = None):
        """
        Sauvegarde les résultats du benchmark.
        
        Args:
            results: Résultats à sauvegarder
            filename: Nom du fichier (optionnel)
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"benchmark_results_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2)
            
            if self.verbose:
                print(f"💾 Résultats sauvegardés: {filename}")
                
        except Exception as e:
            if self.verbose:
                print(f"❌ Erreur sauvegarde: {e}")
    
    def plot_metrics(self, title: str = "Utilisation des ressources"):
        """
        Affiche un graphique des métriques collectées.
        
        Args:
            title: Titre du graphique
        """
        if not self.metrics['cpu_usage']:
            print("⚠️ Aucune métrique à afficher")
            return
        
        try:
            # Convertir les timestamps en temps relatifs
            start_time = self.metrics['timestamps'][0]
            relative_times = [(t - start_time) for t in self.metrics['timestamps']]
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
            
            # CPU Usage
            ax1.plot(relative_times, self.metrics['cpu_usage'], 'b-', linewidth=2)
            ax1.set_ylabel('CPU Usage (%)')
            ax1.set_title(f'{title} - CPU')
            ax1.grid(True, alpha=0.3)
            ax1.set_ylim(0, 100)
            
            # Memory Usage
            ax2.plot(relative_times, self.metrics['memory_usage'], 'r-', linewidth=2)
            ax2.set_ylabel('Memory Usage (%)')
            ax2.set_xlabel('Time (seconds)')
            ax2.set_title(f'{title} - Memory')
            ax2.grid(True, alpha=0.3)
            ax2.set_ylim(0, 100)
            
            plt.tight_layout()
            
            # Sauvegarder le graphique
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_plot_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            
            if self.verbose:
                print(f"📊 Graphique sauvegardé: {filename}")
            
            plt.show()
            
        except ImportError:
            if self.verbose:
                print("⚠️ Matplotlib non disponible - pas de graphique")
        except Exception as e:
            if self.verbose:
                print(f"❌ Erreur création graphique: {e}")


def main():
    """
    Fonction principale du benchmark.
    """
    print("🚀 Benchmark des performances d'entraînement")
    print("=" * 60)
    
    benchmark = PerformanceBenchmark(verbose=True)
    
    # Informations système
    print("🖥️ Informations système:")
    print(f"   CPU: {psutil.cpu_count()} cœurs logiques")
    print(f"   Mémoire: {psutil.virtual_memory().total / (1024**3):.1f}GB")
    print(f"   Utilisation CPU actuelle: {psutil.cpu_percent(interval=1):.1f}%")
    print(f"   Utilisation mémoire: {psutil.virtual_memory().percent:.1f}%")
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'system_info': {
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent
        }
    }
    
    # Benchmark TensorFlow
    print("\n" + "=" * 60)
    tf_results = benchmark.benchmark_tensorflow_operations(iterations=50)
    results['tensorflow'] = tf_results
    
    # Benchmark Optuna
    print("\n" + "=" * 60)
    optuna_results = benchmark.benchmark_optuna_performance(n_trials=30)
    results['optuna'] = optuna_results
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📋 Résumé des performances:")
    
    if tf_results:
        print(f"   🧮 TensorFlow MatMul: {tf_results['matmul']['gflops']} GFLOPS")
        print(f"   🧠 TensorFlow LSTM: {tf_results['lstm']['samples_per_second']} échantillons/sec")
        print(f"   🖥️ CPU moyen (MatMul): {tf_results['matmul']['metrics'].get('cpu_mean', 'N/A')}%")
        print(f"   🖥️ CPU moyen (LSTM): {tf_results['lstm']['metrics'].get('cpu_mean', 'N/A')}%")
    
    if optuna_results:
        print(f"   🎯 Optuna séquentiel: {optuna_results['sequential']['trials_per_second']} trials/sec")
        print(f"   🎯 Optuna parallèle: {optuna_results['parallel']['trials_per_second']} trials/sec")
        print(f"   🚀 Accélération: {optuna_results['parallel']['speedup']}x")
    
    # Sauvegarder les résultats
    benchmark.save_results(results)
    
    # Recommandations
    print("\n💡 Recommandations:")
    
    if tf_results and tf_results['matmul']['metrics'].get('cpu_mean', 0) < 50:
        print("   - Utilisation CPU faible pour TensorFlow - vérifiez la configuration des threads")
    
    if optuna_results and optuna_results['parallel']['speedup'] < 1.5:
        print("   - Faible accélération parallèle - considérez optimiser la charge par trial")
    
    if results['system_info']['memory_percent'] > 80:
        print("   - Utilisation mémoire élevée - réduisez la taille des batches")
    
    print("\n✅ Benchmark terminé!")


if __name__ == "__main__":
    main()
