"""
Optimiseur de performances pour l'entraînement des modèles
=========================================================

Ce module fournit des outils pour optimiser les performances d'entraînement
en ajustant automatiquement les paramètres selon les ressources système.

Utilisation:
    from common.performance_optimizer import PerformanceOptimizer
    optimizer = PerformanceOptimizer()
    config = optimizer.get_optimal_config()
"""

import os
import psutil
import multiprocessing
import math
from typing import Dict, Any, Optional, Tuple
import warnings


class PerformanceOptimizer:
    """
    Optimiseur de performances pour l'entraînement des modèles.
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialise l'optimiseur de performances.
        
        Args:
            verbose: Afficher les informations détaillées
        """
        self.verbose = verbose
        self.system_info = self._get_system_info()
        
        if self.verbose:
            self._print_system_info()
    
    def _get_system_info(self) -> Dict[str, Any]:
        """
        Récupère les informations système.
        
        Returns:
            Dict avec les informations système
        """
        try:
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_count_physical = psutil.cpu_count(logical=False)
            memory = psutil.virtual_memory()
            
            return {
                'cpu_logical': cpu_count_logical,
                'cpu_physical': cpu_count_physical,
                'memory_total_gb': round(memory.total / (1024**3), 2),
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'memory_percent_used': memory.percent,
                'cpu_freq': psutil.cpu_freq().max if psutil.cpu_freq() else None
            }
        except Exception as e:
            if self.verbose:
                print(f"⚠️ Erreur lors de la détection système: {e}")
            
            # Valeurs par défaut conservatrices
            return {
                'cpu_logical': multiprocessing.cpu_count(),
                'cpu_physical': multiprocessing.cpu_count(),
                'memory_total_gb': 8.0,
                'memory_available_gb': 4.0,
                'memory_percent_used': 50.0,
                'cpu_freq': None
            }
    
    def _print_system_info(self):
        """
        Affiche les informations système.
        """
        info = self.system_info
        print("🖥️ Informations système:")
        print(f"   CPU: {info['cpu_logical']} cœurs logiques, {info['cpu_physical']} cœurs physiques")
        print(f"   Mémoire: {info['memory_available_gb']:.1f}GB disponible / {info['memory_total_gb']:.1f}GB total")
        print(f"   Utilisation mémoire: {info['memory_percent_used']:.1f}%")
        if info['cpu_freq']:
            print(f"   Fréquence CPU max: {info['cpu_freq']:.0f}MHz")
    
    def get_optimal_batch_size(self, model_complexity: str = "medium", 
                              sequence_length: int = 50, 
                              n_features: int = 20) -> int:
        """
        Calcule la taille de batch optimale selon les ressources.
        
        Args:
            model_complexity: Complexité du modèle ("low", "medium", "high")
            sequence_length: Longueur des séquences
            n_features: Nombre de features
            
        Returns:
            Taille de batch optimale
        """
        memory_gb = self.system_info['memory_available_gb']
        
        # Estimation de la mémoire par échantillon (en MB)
        complexity_factors = {"low": 0.5, "medium": 1.0, "high": 2.0}
        factor = complexity_factors.get(model_complexity, 1.0)
        
        memory_per_sample = (sequence_length * n_features * 4 * factor) / (1024 * 1024)  # 4 bytes par float32
        
        # Utiliser 60% de la mémoire disponible pour les batches
        available_memory_mb = memory_gb * 1024 * 0.6
        
        # Calculer la taille de batch
        batch_size = int(available_memory_mb / memory_per_sample)
        
        # Contraintes pratiques
        batch_size = max(16, min(batch_size, 512))  # Entre 16 et 512
        
        # Arrondir à la puissance de 2 la plus proche pour optimiser les performances
        batch_size = 2 ** int(math.log2(batch_size))
        
        if self.verbose:
            print(f"📊 Taille de batch optimale: {batch_size}")
            print(f"   Mémoire estimée par échantillon: {memory_per_sample:.2f}MB")
            print(f"   Mémoire totale estimée: {batch_size * memory_per_sample:.1f}MB")
        
        return batch_size
    
    def get_optimal_workers(self, task_type: str = "training") -> int:
        """
        Calcule le nombre optimal de workers selon le type de tâche.
        
        Args:
            task_type: Type de tâche ("training", "optimization", "data_loading")
            
        Returns:
            Nombre optimal de workers
        """
        cpu_logical = self.system_info['cpu_logical']
        memory_gb = self.system_info['memory_available_gb']
        
        if task_type == "training":
            # Pour l'entraînement, limiter selon la mémoire
            if memory_gb < 4:
                workers = 1
            elif memory_gb < 8:
                workers = min(2, cpu_logical // 2)
            elif memory_gb < 16:
                workers = min(4, cpu_logical // 2)
            else:
                workers = min(cpu_logical // 2, 8)  # Max 8 workers pour l'entraînement
                
        elif task_type == "optimization":
            # Pour l'optimisation Optuna, plus de parallélisme possible
            if memory_gb < 4:
                workers = 1
            elif memory_gb < 8:
                workers = min(2, cpu_logical // 2)
            else:
                workers = min(cpu_logical - 1, 12)  # Laisser 1 cœur libre
                
        elif task_type == "data_loading":
            # Pour le chargement de données, plus de workers possible
            workers = min(cpu_logical, 16)
            
        else:
            workers = min(cpu_logical // 2, 4)
        
        workers = max(1, workers)  # Au minimum 1 worker
        
        if self.verbose:
            print(f"👷 Workers optimaux pour {task_type}: {workers}")
        
        return workers
    
    def get_optimal_epochs(self, dataset_size: int, model_complexity: str = "medium") -> Tuple[int, int]:
        """
        Calcule le nombre d'epochs et la patience optimaux.
        
        Args:
            dataset_size: Taille du dataset
            model_complexity: Complexité du modèle
            
        Returns:
            Tuple (epochs_max, patience)
        """
        # Ajuster selon la taille du dataset
        if dataset_size < 1000:
            base_epochs = 50
            base_patience = 8
        elif dataset_size < 5000:
            base_epochs = 40
            base_patience = 6
        elif dataset_size < 10000:
            base_epochs = 30
            base_patience = 5
        else:
            base_epochs = 25
            base_patience = 4
        
        # Ajuster selon la complexité
        complexity_factors = {"low": 0.8, "medium": 1.0, "high": 1.3}
        factor = complexity_factors.get(model_complexity, 1.0)
        
        epochs = int(base_epochs * factor)
        patience = int(base_patience * factor)
        
        # Contraintes
        epochs = max(10, min(epochs, 100))
        patience = max(3, min(patience, 15))
        
        if self.verbose:
            print(f"📈 Epochs optimaux: {epochs}, Patience: {patience}")
        
        return epochs, patience
    
    def get_optimal_optuna_config(self, n_trials: int, dataset_size: int) -> Dict[str, Any]:
        """
        Configure Optuna pour des performances optimales.
        
        Args:
            n_trials: Nombre de trials souhaités
            dataset_size: Taille du dataset
            
        Returns:
            Configuration Optuna optimisée
        """
        workers = self.get_optimal_workers("optimization")
        memory_gb = self.system_info['memory_available_gb']
        
        # Ajuster le nombre de trials selon les ressources
        if memory_gb < 4:
            max_trials = min(n_trials, 50)
            n_jobs = 1
        elif memory_gb < 8:
            max_trials = min(n_trials, 100)
            n_jobs = min(workers, 2)
        else:
            max_trials = n_trials
            n_jobs = min(workers, 4)  # Limiter pour éviter la surcharge mémoire
        
        # Startup trials (exploration initiale)
        n_startup_trials = max(5, min(int(max_trials * 0.15), 20))
        
        # Pruner configuration
        if dataset_size < 1000:
            pruner_config = {
                'n_startup_trials': max(3, int(max_trials * 0.1)),
                'n_warmup_steps': 5,
                'interval_steps': 2
            }
        else:
            pruner_config = {
                'n_startup_trials': max(5, int(max_trials * 0.05)),
                'n_warmup_steps': 8,
                'interval_steps': 3
            }
        
        config = {
            'n_trials': max_trials,
            'n_jobs': n_jobs,
            'n_startup_trials': n_startup_trials,
            'pruner_config': pruner_config,
            'timeout': None,  # Pas de timeout par défaut
            'show_progress_bar': self.verbose
        }
        
        if self.verbose:
            print(f"🎯 Configuration Optuna optimisée:")
            print(f"   Trials: {max_trials}, Jobs parallèles: {n_jobs}")
            print(f"   Startup trials: {n_startup_trials}")
            print(f"   Pruner: {pruner_config}")
        
        return config
    
    def get_tensorflow_memory_config(self) -> Dict[str, Any]:
        """
        Configure la gestion mémoire de TensorFlow.
        
        Returns:
            Configuration mémoire TensorFlow
        """
        memory_gb = self.system_info['memory_available_gb']
        
        if memory_gb < 4:
            # Configuration conservatrice
            config = {
                'memory_growth': True,
                'memory_limit_mb': int(memory_gb * 1024 * 0.7),  # 70% de la mémoire
                'allow_soft_placement': True,
                'log_device_placement': False
            }
        elif memory_gb < 8:
            # Configuration équilibrée
            config = {
                'memory_growth': True,
                'memory_limit_mb': int(memory_gb * 1024 * 0.8),  # 80% de la mémoire
                'allow_soft_placement': True,
                'log_device_placement': False
            }
        else:
            # Configuration performante
            config = {
                'memory_growth': False,  # Allouer toute la mémoire d'un coup
                'memory_limit_mb': None,  # Pas de limite
                'allow_soft_placement': True,
                'log_device_placement': False
            }
        
        if self.verbose:
            print(f"💾 Configuration mémoire TensorFlow: {config}")
        
        return config
    
    def get_optimal_config(self, 
                          model_complexity: str = "medium",
                          dataset_size: int = 5000,
                          sequence_length: int = 50,
                          n_features: int = 20,
                          n_trials: int = 100) -> Dict[str, Any]:
        """
        Génère une configuration complète optimisée.
        
        Args:
            model_complexity: Complexité du modèle
            dataset_size: Taille du dataset
            sequence_length: Longueur des séquences
            n_features: Nombre de features
            n_trials: Nombre de trials Optuna
            
        Returns:
            Configuration complète optimisée
        """
        if self.verbose:
            print("🚀 Génération de la configuration optimisée")
            print("=" * 50)
        
        batch_size = self.get_optimal_batch_size(model_complexity, sequence_length, n_features)
        training_workers = self.get_optimal_workers("training")
        data_workers = self.get_optimal_workers("data_loading")
        epochs, patience = self.get_optimal_epochs(dataset_size, model_complexity)
        optuna_config = self.get_optimal_optuna_config(n_trials, dataset_size)
        tf_memory_config = self.get_tensorflow_memory_config()
        
        config = {
            'system_info': self.system_info,
            'training': {
                'batch_size': batch_size,
                'epochs': epochs,
                'patience': patience,
                'workers': training_workers,
                'use_multiprocessing': training_workers > 1
            },
            'data_loading': {
                'workers': data_workers,
                'prefetch_factor': 2 if data_workers > 1 else None
            },
            'optuna': optuna_config,
            'tensorflow': tf_memory_config,
            'recommendations': self._get_recommendations()
        }
        
        if self.verbose:
            print("✅ Configuration générée avec succès")
        
        return config
    
    def _get_recommendations(self) -> list:
        """
        Génère des recommandations d'optimisation.
        
        Returns:
            Liste de recommandations
        """
        recommendations = []
        info = self.system_info
        
        if info['cpu_logical'] < 4:
            recommendations.append("💡 Processeur: Considérez un CPU avec plus de cœurs")
        
        if info['memory_available_gb'] < 4:
            recommendations.append("💡 Mémoire: Augmentez la RAM à 8GB+ pour de meilleures performances")
        
        if info['memory_percent_used'] > 80:
            recommendations.append("⚠️ Mémoire: Utilisation élevée - fermez les applications non nécessaires")
        
        if info['cpu_logical'] >= 8 and info['memory_available_gb'] >= 16:
            recommendations.append("🚀 Système performant: Vous pouvez utiliser des modèles plus complexes")
        
        return recommendations


def apply_performance_optimizations(config: Dict[str, Any], verbose: bool = False):
    """
    Applique les optimisations de performance au système.
    
    Args:
        config: Configuration générée par PerformanceOptimizer
        verbose: Afficher les informations détaillées
    """
    if verbose:
        print("🔧 Application des optimisations de performance")
    
    # Optimisations des variables d'environnement
    tf_config = config.get('tensorflow', {})
    
    if 'memory_limit_mb' in tf_config and tf_config['memory_limit_mb']:
        os.environ['TF_MEMORY_LIMIT'] = str(tf_config['memory_limit_mb'])
    
    # Optimisations pour les bibliothèques numériques
    training_workers = config.get('training', {}).get('workers', 1)
    
    # Ajuster les threads selon le nombre de workers d'entraînement
    if training_workers > 1:
        # Réduire les threads par processus quand on a plusieurs workers
        threads_per_process = max(1, config['system_info']['cpu_logical'] // training_workers)
        os.environ['OMP_NUM_THREADS'] = str(threads_per_process)
        os.environ['MKL_NUM_THREADS'] = str(threads_per_process)
        os.environ['OPENBLAS_NUM_THREADS'] = str(threads_per_process)
    
    if verbose:
        print("✅ Optimisations appliquées")


if __name__ == "__main__":
    # Test de l'optimiseur
    optimizer = PerformanceOptimizer(verbose=True)
    config = optimizer.get_optimal_config()
    
    print("\n📋 Configuration complète:")
    for section, values in config.items():
        if section != 'system_info':
            print(f"\n{section.upper()}:")
            for key, value in values.items():
                print(f"  {key}: {value}")
    
    if config['recommendations']:
        print("\n💡 RECOMMANDATIONS:")
        for rec in config['recommendations']:
            print(f"  {rec}")
