#!/usr/bin/env python3
"""
Script de test pour la configuration TensorFlow optimisée
=========================================================

Ce script teste la nouvelle configuration TensorFlow et compare les performances
avec l'ancienne configuration.

Usage:
    python test_tensorflow_config.py
"""

import time
import os
import sys
import numpy as np
from datetime import datetime

def test_tensorflow_config():
    """
    Teste la configuration TensorFlow optimisée.
    """
    print("🧪 Test de la configuration TensorFlow optimisée")
    print("=" * 60)
    
    try:
        # Importer la configuration
        from common.tensorflow_config import configure_tensorflow_for_cpu, get_system_info
        
        # Configurer TensorFlow
        print("📋 Configuration du système:")
        config_info = configure_tensorflow_for_cpu(verbose=True)
        
        print("\n" + "=" * 60)
        print("🚀 Test des performances TensorFlow")
        
        # Importer TensorFlow après configuration
        import tensorflow as tf
        
        # Vérifier la configuration
        print(f"📊 Version TensorFlow: {tf.__version__}")
        print(f"🔧 Threads intra-op: {tf.config.threading.get_intra_op_parallelism_threads()}")
        print(f"🔧 Threads inter-op: {tf.config.threading.get_inter_op_parallelism_threads()}")
        print(f"⚡ JIT activé: {tf.config.optimizer.get_jit()}")
        
        # Test de performance simple
        print("\n🏃 Test de performance (multiplication de matrices)...")
        
        # Créer des matrices de test
        size = 1000
        a = tf.random.normal((size, size), dtype=tf.float32)
        b = tf.random.normal((size, size), dtype=tf.float32)
        
        # Warm-up
        _ = tf.matmul(a, b)
        
        # Test de performance
        start_time = time.time()
        n_iterations = 10
        
        for i in range(n_iterations):
            result = tf.matmul(a, b)
            if i == 0:
                print(f"   Forme du résultat: {result.shape}")
        
        end_time = time.time()
        avg_time = (end_time - start_time) / n_iterations
        
        print(f"   ⏱️ Temps moyen par multiplication: {avg_time:.4f}s")
        print(f"   🚀 Performance: {(size*size*size)/(avg_time*1e9):.2f} GFLOPS")
        
        # Test de modèle simple
        print("\n🧠 Test de modèle simple (LSTM)...")
        
        # Créer un modèle simple
        model = tf.keras.Sequential([
            tf.keras.layers.LSTM(64, return_sequences=True, input_shape=(10, 5)),
            tf.keras.layers.LSTM(32),
            tf.keras.layers.Dense(1)
        ])
        
        model.compile(optimizer='adam', loss='mse')
        
        # Données de test
        X_test = np.random.random((100, 10, 5)).astype(np.float32)
        y_test = np.random.random((100, 1)).astype(np.float32)
        
        # Test d'entraînement
        start_time = time.time()
        history = model.fit(X_test, y_test, epochs=5, batch_size=32, verbose=0)
        training_time = time.time() - start_time
        
        print(f"   ⏱️ Temps d'entraînement (5 epochs): {training_time:.2f}s")
        print(f"   📉 Loss finale: {history.history['loss'][-1]:.6f}")
        
        # Test de prédiction
        start_time = time.time()
        predictions = model.predict(X_test, verbose=0)
        prediction_time = time.time() - start_time
        
        print(f"   ⏱️ Temps de prédiction: {prediction_time:.4f}s")
        print(f"   📊 Prédictions shape: {predictions.shape}")
        
        print("\n✅ Configuration TensorFlow testée avec succès!")
        
        # Recommandations
        recommendations = []
        system_info = config_info['system_info']
        
        if system_info['cpu_logical'] < 4:
            recommendations.append("💡 Considérez un processeur avec plus de cœurs")
        
        if system_info['memory_available_gb'] < 8:
            recommendations.append("💡 Plus de RAM améliorerait les performances")
        
        if recommendations:
            print("\n📋 Recommandations:")
            for rec in recommendations:
                print(f"   {rec}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print("   Assurez-vous que TensorFlow est installé:")
        print("   pip install tensorflow")
        return False
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False


def benchmark_old_vs_new():
    """
    Compare les performances entre l'ancienne et la nouvelle configuration.
    """
    print("\n🏁 Benchmark: Ancienne vs Nouvelle configuration")
    print("=" * 60)
    
    try:
        import tensorflow as tf
        
        # Test avec la nouvelle configuration (déjà appliquée)
        print("🆕 Test avec la nouvelle configuration...")
        
        size = 500
        a = tf.random.normal((size, size), dtype=tf.float32)
        b = tf.random.normal((size, size), dtype=tf.float32)
        
        # Warm-up
        _ = tf.matmul(a, b)
        
        # Benchmark
        start_time = time.time()
        n_iterations = 20
        
        for _ in range(n_iterations):
            _ = tf.matmul(a, b)
        
        new_config_time = (time.time() - start_time) / n_iterations
        
        print(f"   ⏱️ Temps moyen: {new_config_time:.4f}s")
        print(f"   🚀 Performance: {(size*size*size)/(new_config_time*1e9):.2f} GFLOPS")
        
        # Note: Pour tester l'ancienne configuration, il faudrait redémarrer Python
        # avec l'ancienne configuration, ce qui n'est pas pratique dans ce script
        
        print("\n📝 Note: Pour comparer avec l'ancienne configuration,")
        print("   lancez ce script avant et après avoir appliqué les modifications.")
        
        return new_config_time
        
    except Exception as e:
        print(f"❌ Erreur lors du benchmark: {e}")
        return None


def monitor_system_resources():
    """
    Surveille l'utilisation des ressources système pendant un test.
    """
    print("\n📊 Surveillance des ressources système")
    print("=" * 60)
    
    try:
        import psutil
        import threading
        import time
        
        # Variables pour stocker les métriques
        cpu_usage = []
        memory_usage = []
        monitoring = True
        
        def monitor():
            while monitoring:
                cpu_usage.append(psutil.cpu_percent(interval=0.1))
                memory_usage.append(psutil.virtual_memory().percent)
                time.sleep(0.1)
        
        # Démarrer la surveillance
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        
        # Effectuer un test intensif
        print("🔥 Test intensif en cours...")
        
        import tensorflow as tf
        
        # Test avec plusieurs opérations simultanées
        size = 800
        matrices = [tf.random.normal((size, size), dtype=tf.float32) for _ in range(4)]
        
        start_time = time.time()
        
        for i in range(10):
            results = []
            for j in range(len(matrices)):
                for k in range(j+1, len(matrices)):
                    result = tf.matmul(matrices[j], matrices[k])
                    results.append(result)
            
            if i % 2 == 0:
                print(f"   Itération {i+1}/10 terminée")
        
        test_duration = time.time() - start_time
        
        # Arrêter la surveillance
        monitoring = False
        time.sleep(0.2)  # Laisser le temps au thread de se terminer
        
        # Analyser les résultats
        if cpu_usage and memory_usage:
            avg_cpu = sum(cpu_usage) / len(cpu_usage)
            max_cpu = max(cpu_usage)
            avg_memory = sum(memory_usage) / len(memory_usage)
            max_memory = max(memory_usage)
            
            print(f"\n📈 Résultats de surveillance:")
            print(f"   ⏱️ Durée du test: {test_duration:.2f}s")
            print(f"   🖥️ CPU moyen: {avg_cpu:.1f}%")
            print(f"   🖥️ CPU max: {max_cpu:.1f}%")
            print(f"   💾 Mémoire moyenne: {avg_memory:.1f}%")
            print(f"   💾 Mémoire max: {max_memory:.1f}%")
            
            # Évaluation de l'utilisation
            if avg_cpu > 80:
                print("   🔥 Excellente utilisation du CPU!")
            elif avg_cpu > 50:
                print("   👍 Bonne utilisation du CPU")
            else:
                print("   ⚠️ Utilisation du CPU faible - possibilité d'optimisation")
        
        return {
            'test_duration': test_duration,
            'avg_cpu': avg_cpu if cpu_usage else 0,
            'max_cpu': max_cpu if cpu_usage else 0,
            'avg_memory': avg_memory if memory_usage else 0,
            'max_memory': max_memory if memory_usage else 0
        }
        
    except ImportError:
        print("⚠️ psutil non disponible - surveillance désactivée")
        return None
    except Exception as e:
        print(f"❌ Erreur lors de la surveillance: {e}")
        return None


def main():
    """
    Fonction principale du script de test.
    """
    print(f"🚀 Test de configuration TensorFlow - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Test de base
    success = test_tensorflow_config()
    
    if success:
        # Benchmark
        benchmark_old_vs_new()
        
        # Surveillance des ressources
        monitor_system_resources()
        
        print("\n" + "=" * 80)
        print("✅ Tous les tests terminés avec succès!")
        print("\n💡 Conseils pour optimiser davantage:")
        print("   1. Ajustez la taille des batches selon votre RAM")
        print("   2. Utilisez des modèles plus petits si la mémoire est limitée")
        print("   3. Surveillez l'utilisation CPU pendant l'entraînement")
        print("   4. Considérez la parallélisation des données si possible")
        
    else:
        print("\n❌ Échec des tests - vérifiez votre installation TensorFlow")
        sys.exit(1)


if __name__ == "__main__":
    main()
